/**
 * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially useful
 * for Docker builds.
 */
// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import "./src/env.js";
import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin('./src/i18n.ts');

/** @type {import("next").NextConfig} */

// DeerFlow leverages **Turbopack** during development for faster builds and a smoother developer experience.
// However, in production, **Webpack** is used instead.
//
// This decision is based on the current recommendation to avoid using Turbopack for critical projects, as it
// is still evolving and may not yet be fully stable for production environments.

const config = {
  // For development mode
  turbopack: {
    rules: {
      "*.md": {
        loaders: ["raw-loader"],
        as: "*.js",
      },
    },
  },

  // For production mode
  webpack: (config, { isServer }) => {
    // Add markdown loader
    config.module.rules.push({
      test: /\.md$/,
      use: "raw-loader",
    });

    // Remove hash from static asset filenames for client-side builds
    // This enables overwrite-style CDN deployment with version control through build platform
    if (!isServer) {
      // JavaScript files - remove contenthash from filenames
      config.output.filename = 'static/js/[name].js';
      config.output.chunkFilename = 'static/chunks/[name].js';

      // Static assets (images, fonts, etc.) - remove hash from filenames
      config.output.assetModuleFilename = 'static/media/[name][ext]';


    }

    return config;
  },

  // ... rest of the configuration.
  output: "standalone",
};

export default withNextIntl(config);
